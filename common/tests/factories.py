from unittest.mock import MagicMock

import factory.fuzzy
from django.contrib.auth import get_user_model
from django.contrib.auth.models import AnonymousUser, Permission
from django.db.models import Q
from django.test import TestCase
from django.utils.translation import activate
from faker import Faker as FakerClass
from gabbro.acl.models import Role
from graphene.test import Client

from app.schema import schema
from chat_ai.models import Conversation, Message
from layers.models import Layer, Record
from organizations.models import Organization
from workspaces.models import Workspace, WorkspaceRequest, Dataset

User = get_user_model()
fake = FakerClass()


class UserFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = User


class WorkspaceFactory(factory.django.DjangoModelFactory):
    owner = factory.SubFactory(UserFactory)

    class Meta:
        model = Workspace


class DatasetFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Dataset


class WorkspaceRequestFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = WorkspaceRequest


class OrganizationFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Organization


class RoleFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Role


class LayerFactory(factory.django.DjangoModelFactory):
    workspace = factory.SubFactory(WorkspaceFactory)
    key = factory.LazyAttribute(lambda _: fake.slug())
    title = factory.LazyAttribute(lambda _: fake.sentence(nb_words=3))
    description = factory.LazyAttribute(lambda _: fake.paragraph())
    read_only = False
    status = "published"

    class Meta:
        model = Layer


class RecordFactory(factory.django.DjangoModelFactory):
    layer = factory.SubFactory(LayerFactory)
    source_properties = factory.LazyFunction(
        lambda: {
            "city": fake.city(),
            "name": fake.name(),
            "poi_type": fake.random_element(elements=("schools", "hospitals", "parks")),
            "confidence": fake.random_element(elements=("High", "Medium", "Low")),
            "point_id": fake.random_int(min=1, max=1000),
            "parcel_id": fake.random_int(min=1000, max=9999),
        }
    )
    map_data = factory.LazyFunction(
        lambda: {
            "summary_field_1": fake.word(),
            "summary_field_2": fake.random_int(min=1, max=100),
        }
    )
    data = factory.LazyFunction(
        lambda: {
            "field1": fake.word(),
            "field2": fake.random_int(min=1, max=100),
            "field3": fake.boolean(),
        }
    )
    weight = factory.LazyAttribute(lambda _: fake.random_int(min=1, max=100))

    class Meta:
        model = Record


class ConversationFactory(factory.django.DjangoModelFactory):
    """Factory for creating Conversation instances for testing."""

    user = factory.SubFactory(UserFactory)

    class Meta:
        model = Conversation


class MessageFactory(factory.django.DjangoModelFactory):
    """Factory for creating Message instances for testing."""

    conversation = factory.SubFactory(ConversationFactory)
    message = factory.LazyFunction(
        lambda: {
            "user_input": fake.text(),
            "sql_query": f"SELECT * FROM layers_record WHERE layer_id = {fake.random_int(min=1, max=100)};",
            "sql_result": [],
            "final_result": fake.text(),
        }
    )

    class Meta:
        model = Message


class BaseTestMixin(TestCase):
    def setUp(self):
        activate("en")
        self.user = UserFactory(is_superuser=False)
        self.superuser = UserFactory(
            is_superuser=True,
        )
        self.client = Client(schema)
        self.auth_request = MagicMock()
        self.super_user_auth_request = MagicMock()
        self.super_user_auth_request.user = self.superuser
        self.auth_request.user = self.user
        self.role_manager = RoleFactory(codename="Manager")
        permissions = Permission.objects.filter(
            (Q(content_type__model="organization"))
            & (
                Q(codename__endswith="_user")
                | Q(codename__endswith="_workspace")
                | Q(codename__endswith="_role")
            )
        )
        self.role_manager.permissions.set(permissions)
        self.organization = OrganizationFactory()
        self.organization.roles.add(self.role_manager)
        self.organization.acl_add_user(user=self.user, roles=[self.role_manager])
        self.non_auth_request = MagicMock()
        self.non_auth_request.user = AnonymousUser()
        self.maxDiff = None
