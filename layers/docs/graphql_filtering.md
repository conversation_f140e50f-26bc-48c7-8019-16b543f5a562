# GraphQL Filtering with django-filter

This document explains how to use the django-filter integration with GraphQL to filter records in the GeoCORE backend.

## Overview

The Record model now supports advanced filtering capabilities through django-filter. This allows you to filter records by various fields and criteria in your GraphQL queries.

## Available Filters

The following filters are available for the Record model:

### Basic Filters

- `id`: Filter by record ID
- `layerId`: Filter by layer ID
- `weight`: Filter by weight value

### Range Filters for Weight

- `weightGt`: Filter records with weight greater than a value
- `weightLt`: Filter records with weight less than a value
- `weightGte`: Filter records with weight greater than or equal to a value
- `weightLte`: Filter records with weight less than or equal to a value

### Date Filters

- `createdAfter`: Filter records created after a specific date/time
- `createdBefore`: Filter records created before a specific date/time
- `modifiedAfter`: Filter records modified after a specific date/time
- `modifiedBefore`: Filter records modified before a specific date/time

### JSON Field Filters

- `search`: Search across all JSON fields (data, map_data, source_properties)
- `dataContains`: Filter records where the data JSON field contains a specific value
- `mapDataContains`: Filter records where the map_data JSON field contains a specific value
- `sourcePropertiesContains`: Filter records where the source_properties JSON field contains a specific value

## Example Queries

### Basic Filtering

```graphql
query {
  records(
    orgId: 1,
    layerId: 123,
    id: 456
  ) {
    data {
      id
      data
      mapData
    }
    count
  }
}
```

### Filtering by Weight Range

```graphql
query {
  records(
    orgId: 1,
    layerId: 123,
    weightGte: 10,
    weightLte: 50
  ) {
    data {
      id
      weight
    }
    count
  }
}
```

### Filtering by Date Range

```graphql
query {
  records(
    orgId: 1,
    layerId: 123,
    createdAfter: "2025-01-01T00:00:00Z",
    createdBefore: "2025-07-31T23:59:59Z"
  ) {
    data {
      id
      created
    }
    count
  }
}
```

### Searching in JSON Fields

```graphql
query {
  records(
    orgId: 1,
    layerId: 123,
    search: "keyword"
  ) {
    data {
      id
      data
      mapData
      sourceProperties
    }
    count
  }
}
```

### Filtering by Specific JSON Field Content

```graphql
query {
  records(
    orgId: 1,
    layerId: 123,
    dataContains: "value"
  ) {
    data {
      id
      data
    }
    count
  }
}
```

## Combining Filters

You can combine multiple filters in a single query to narrow down your results:

```graphql
query {
  records(
    orgId: 1,
    layerId: 123,
    weightGte: 10,
    createdAfter: "2025-01-01T00:00:00Z",
    dataContains: "value"
  ) {
    data {
      id
      weight
      created
      data
    }
    count
  }
}
```

## Pagination

Pagination still works with filters. Use the `pageInfo` parameter to control pagination:

```graphql
query {
  records(
    orgId: 1,
    layerId: 123,
    search: "keyword",
    pageInfo: {
      limit: 10,
      offset: 0,
      orderBy: "created"
    }
  ) {
    data {
      id
      created
    }
    count
  }
}
```

## Notes

- All filters are optional. If you don't specify any filters, all records for the specified layer will be returned.
- Filters are applied with AND logic, meaning records must match all specified filters.
- The `search` filter searches across all JSON fields (data, map_data, source_properties).
- Date filters expect ISO format dates (YYYY-MM-DDTHH:MM:SSZ).