"""
Example implementation of django-filter with GraphQL for the Record model.

This file demonstrates how to use the BaseFilterQuery class, create_filter_field function,
and resolve_filtered_query function with the Record model.
"""

import graphene
from common.utils import (
    PageInfo,
    BaseFilterQuery,
    create_filter_field,
    resolve_filtered_query,
    authentication_required,
    organization_required,
    authorize_user,
)
from layers.models import Record
from layers.schema.filters import RecordFilter
from layers.schema.object_types import RecordListType
from organizations.perms_constants import VIEW_WORKSPACE


class RecordFilterQuery(BaseFilterQuery):
    """
    Example query that uses the BaseFilterQuery class to filter records.
    
    This query automatically adds filter fields from the RecordFilter class
    to the records field.
    """
    
    # Create a field with filter arguments from the RecordFilter class
    records = create_filter_field(
        RecordListType,
        RecordFilter,
        org_id=graphene.Int(required=True),
        layer_id=graphene.Int(required=True),
        page_info=PageInfo(),
    )
    
    @staticmethod
    @authentication_required
    @organization_required
    def resolve_records(root, info, org_id, layer_id, **kwargs):
        """
        Resolve the records field with filtering.
        
        This method demonstrates how to use the resolve_filtered_query function
        to apply filters, pagination, and ordering to a queryset.
        """
        user = info.context.user
        organization = info.context.organization
        authorize_user(model_obj=organization, user=user, permission=VIEW_WORKSPACE)
        
        # Get the base queryset
        queryset = Record.objects.filter(layer_id=layer_id)
        
        # Apply filters, pagination, and ordering
        return resolve_filtered_query(queryset, RecordFilter, info, **kwargs)


# Alternative implementation using the apply_filterset_to_queryset function directly
class AlternativeRecordQuery(graphene.ObjectType):
    """
    Alternative query that uses the apply_filterset_to_queryset function directly.
    
    This query manually adds filter arguments to the records field.
    """
    
    records = graphene.Field(
        RecordListType,
        org_id=graphene.Int(required=True),
        layer_id=graphene.Int(required=True),
        page_info=PageInfo(),
        # Add filter arguments manually
        id=graphene.Int(),
        weight=graphene.Int(),
        weight_gt=graphene.Int(),
        weight_lt=graphene.Int(),
        weight_gte=graphene.Int(),
        weight_lte=graphene.Int(),
        created_after=graphene.DateTime(),
        created_before=graphene.DateTime(),
        modified_after=graphene.DateTime(),
        modified_before=graphene.DateTime(),
        search=graphene.String(),
        data_contains=graphene.String(),
        map_data_contains=graphene.String(),
        source_properties_contains=graphene.String(),
    )
    
    @staticmethod
    @authentication_required
    @organization_required
    def resolve_records(root, info, org_id, layer_id, page_info=None, **kwargs):
        """
        Resolve the records field with filtering.
        
        This method demonstrates how to use the apply_filterset_to_queryset function
        directly to apply filters to a queryset.
        """
        user = info.context.user
        organization = info.context.organization
        authorize_user(model_obj=organization, user=user, permission=VIEW_WORKSPACE)
        
        # Get the base queryset
        queryset = Record.objects.filter(layer_id=layer_id)
        
        # Apply filters
        from common.utils import apply_filterset_to_queryset
        filtered_queryset = apply_filterset_to_queryset(RecordFilter, queryset, **info.variable_values)
        
        # Apply pagination
        page_info = page_info or {}
        limit = page_info.get('limit', 100_000)
        offset = page_info.get('offset', 0)
        order_by = page_info.get('order_by', '')
        
        # Apply ordering if specified
        if order_by:
            order_by_field = '__'.join(order_by.strip().split('.'))
            filtered_queryset = filtered_queryset.order_by(order_by_field)
        
        # Get total count before pagination
        total_count = filtered_queryset.count()
        
        # Apply pagination
        paginated_queryset = filtered_queryset[offset:offset + limit]
        
        return RecordListType(
            data=paginated_queryset,
            count=total_count
        )