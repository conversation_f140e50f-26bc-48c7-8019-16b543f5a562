"""
Test cases for edge cases and error handling in advanced filtering system.
"""

from django.utils.translation import gettext_lazy as _

from common.tests.factories import (
    BaseTestMixin,
    WorkspaceFactory,
    LayerFactory,
    RecordFactory,
)


class TestAdvancedFiltersEdgeCases(BaseTestMixin):
    def setUp(self):
        super().setUp()
        
        # Create test data
        self.workspace = WorkspaceFactory(
            owner=self.user, organization=self.organization
        )
        self.layer = LayerFactory(workspace=self.workspace)
        self.record = RecordFactory(layer=self.layer)
        
        # Base queries
        self.layers_query = """
        query TestLayersEdgeCases(
            $orgId: Int!,
            $workspaceId: Int!,
            $filterGroups: [FilterGroupInput!]
        ) {
          layers(
            orgId: $orgId,
            workspaceId: $workspaceId,
            filterGroups: $filterGroups
          ) {
            count
            data { id }
          }
        }
        """
        
        self.records_query = """
        query TestRecordsEdgeCases(
            $orgId: Int!,
            $layerId: Int!,
            $filterGroups: [FilterGroupInput!]
        ) {
          records(
            orgId: $orgId,
            layerId: $layerId,
            filterGroups: $filterGroups
          ) {
            count
            data { id }
          }
        }
        """

    def test_empty_filter_group_filters(self):
        """Test filter group with empty filters array."""
        variables = {
            "orgId": self.organization.id,
            "workspaceId": self.workspace.id,
            "filterGroups": [
                {
                    "type": "AND",
                    "isNot": False,
                    "filters": []  # Empty filters array
                }
            ]
        }
        
        response = self.client.execute(
            self.layers_query, variables=variables, context=self.auth_request
        )
        
        self.assertNotIn("errors", response)
        data = response["data"]["layers"]
        
        # Should return all layers when filter group has no filters
        self.assertEqual(data["count"], 1)

    def test_filter_with_missing_field(self):
        """Test filter with missing field parameter."""
        variables = {
            "orgId": self.organization.id,
            "workspaceId": self.workspace.id,
            "filterGroups": [
                {
                    "type": "AND",
                    "isNot": False,
                    "filters": [
                        {
                            "op": "exact",
                            # "field": "status",  # Missing field
                            "value": "published",
                            "isNot": False
                        }
                    ]
                }
            ]
        }
        
        # This should cause a GraphQL validation error due to missing required field
        response = self.client.execute(
            self.layers_query, variables=variables, context=self.auth_request
        )
        
        # GraphQL should catch this as a validation error
        self.assertIn("errors", response)

    def test_filter_with_missing_value(self):
        """Test filter with missing value parameter."""
        variables = {
            "orgId": self.organization.id,
            "workspaceId": self.workspace.id,
            "filterGroups": [
                {
                    "type": "AND",
                    "isNot": False,
                    "filters": [
                        {
                            "op": "exact",
                            "field": "status",
                            # "value": "published",  # Missing value
                            "isNot": False
                        }
                    ]
                }
            ]
        }
        
        # This should cause a GraphQL validation error due to missing required value
        response = self.client.execute(
            self.layers_query, variables=variables, context=self.auth_request
        )
        
        # GraphQL should catch this as a validation error
        self.assertIn("errors", response)

    def test_filter_with_invalid_operator(self):
        """Test filter with invalid operator."""
        variables = {
            "orgId": self.organization.id,
            "workspaceId": self.workspace.id,
            "filterGroups": [
                {
                    "type": "AND",
                    "isNot": False,
                    "filters": [
                        {
                            "op": "invalid_operator",  # Invalid operator
                            "field": "status",
                            "value": "published",
                            "isNot": False
                        }
                    ]
                }
            ]
        }
        
        # This should cause a GraphQL validation error due to invalid enum value
        response = self.client.execute(
            self.layers_query, variables=variables, context=self.auth_request
        )
        
        # GraphQL should catch this as a validation error
        self.assertIn("errors", response)

    def test_filter_with_invalid_group_type(self):
        """Test filter group with invalid type."""
        variables = {
            "orgId": self.organization.id,
            "workspaceId": self.workspace.id,
            "filterGroups": [
                {
                    "type": "INVALID_TYPE",  # Invalid group type
                    "isNot": False,
                    "filters": [
                        {
                            "op": "exact",
                            "field": "status",
                            "value": "published",
                            "isNot": False
                        }
                    ]
                }
            ]
        }
        
        # This should cause a GraphQL validation error due to invalid enum value
        response = self.client.execute(
            self.layers_query, variables=variables, context=self.auth_request
        )
        
        # GraphQL should catch this as a validation error
        self.assertIn("errors", response)

    def test_numeric_filter_with_non_numeric_value(self):
        """Test numeric operator with non-numeric value."""
        variables = {
            "orgId": self.organization.id,
            "layerId": self.layer.id,
            "filterGroups": [
                {
                    "type": "AND",
                    "isNot": False,
                    "filters": [
                        {
                            "op": "gt",
                            "field": "weight",
                            "value": "not_a_number",  # Non-numeric value for gt operator
                            "isNot": False
                        }
                    ]
                }
            ]
        }
        
        response = self.client.execute(
            self.records_query, variables=variables, context=self.auth_request
        )
        
        # The query should execute but may return unexpected results or errors
        # depending on how the conversion function handles invalid input
        # This tests the robustness of the conversion methods
        if "errors" in response:
            # If there's an error, it should be a meaningful one
            self.assertIsInstance(response["errors"], list)
        else:
            # If no error, the filter should be ignored or handled gracefully
            self.assertIn("data", response)

    def test_date_filter_with_invalid_date_format(self):
        """Test date operator with invalid date format."""
        variables = {
            "orgId": self.organization.id,
            "layerId": self.layer.id,
            "filterGroups": [
                {
                    "type": "AND",
                    "isNot": False,
                    "filters": [
                        {
                            "op": "date__gt",
                            "field": "created",
                            "value": "invalid-date-format",  # Invalid date format
                            "isNot": False
                        }
                    ]
                }
            ]
        }
        
        response = self.client.execute(
            self.records_query, variables=variables, context=self.auth_request
        )
        
        # Should handle invalid date format gracefully
        if "errors" in response:
            self.assertIsInstance(response["errors"], list)
        else:
            self.assertIn("data", response)

    def test_deeply_nested_json_field_access(self):
        """Test filtering on deeply nested JSON field properties."""
        # Create a record with deeply nested JSON data
        nested_record = RecordFactory(
            layer=self.layer,
            data={
                "level1": {
                    "level2": {
                        "level3": {
                            "deep_value": "found_it"
                        }
                    }
                }
            }
        )
        
        variables = {
            "orgId": self.organization.id,
            "layerId": self.layer.id,
            "filterGroups": [
                {
                    "type": "AND",
                    "isNot": False,
                    "filters": [
                        {
                            "op": "exact",
                            "field": "data__level1__level2__level3__deep_value",
                            "value": "found_it",
                            "isNot": False
                        }
                    ]
                }
            ]
        }
        
        response = self.client.execute(
            self.records_query, variables=variables, context=self.auth_request
        )
        
        self.assertNotIn("errors", response)
        data = response["data"]["records"]
        
        # Should find the record with the deeply nested value
        self.assertEqual(data["count"], 1)
        self.assertEqual(data["data"][0]["id"], nested_record.id)

    def test_multiple_negations(self):
        """Test multiple levels of negation (group and field)."""
        variables = {
            "orgId": self.organization.id,
            "workspaceId": self.workspace.id,
            "filterGroups": [
                {
                    "type": "AND",
                    "isNot": True,  # Negate the group
                    "filters": [
                        {
                            "op": "exact",
                            "field": "status",
                            "value": "published",
                            "isNot": True  # Also negate the field
                        }
                    ]
                }
            ]
        }
        
        response = self.client.execute(
            self.layers_query, variables=variables, context=self.auth_request
        )
        
        self.assertNotIn("errors", response)
        data = response["data"]["layers"]
        
        # Double negation: NOT(NOT(status = published)) = status = published
        # This tests the logical correctness of nested negations
        self.assertIsInstance(data["count"], int)

    def test_very_large_filter_groups_list(self):
        """Test performance with many filter groups."""
        # Create a large number of filter groups
        large_filter_groups = []
        for i in range(50):  # 50 filter groups
            large_filter_groups.append({
                "type": "OR",
                "isNot": False,
                "filters": [
                    {
                        "op": "exact",
                        "field": "id",
                        "value": str(i),
                        "isNot": False
                    }
                ]
            })
        
        variables = {
            "orgId": self.organization.id,
            "workspaceId": self.workspace.id,
            "filterGroups": large_filter_groups
        }
        
        response = self.client.execute(
            self.layers_query, variables=variables, context=self.auth_request
        )
        
        self.assertNotIn("errors", response)
        data = response["data"]["layers"]
        
        # Should handle large number of filter groups without errors
        self.assertIsInstance(data["count"], int)

    def test_unicode_and_special_characters(self):
        """Test filtering with unicode and special characters."""
        # Create a layer with unicode title
        unicode_layer = LayerFactory(
            workspace=self.workspace,
            title="مدرسة ابن البيطار الثانوية",  # Arabic text
        )
        
        variables = {
            "orgId": self.organization.id,
            "workspaceId": self.workspace.id,
            "filterGroups": [
                {
                    "type": "AND",
                    "isNot": False,
                    "filters": [
                        {
                            "op": "icontains",
                            "field": "title",
                            "value": "البيطار",  # Arabic text
                            "isNot": False
                        }
                    ]
                }
            ]
        }
        
        response = self.client.execute(
            self.layers_query, variables=variables, context=self.auth_request
        )
        
        self.assertNotIn("errors", response)
        data = response["data"]["layers"]
        
        # Should handle unicode text correctly
        self.assertEqual(data["count"], 1)
        self.assertEqual(data["data"][0]["id"], unicode_layer.id)
