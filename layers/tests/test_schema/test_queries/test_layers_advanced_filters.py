"""
Test cases for layers query with advanced filtering system.
"""

from django.utils.translation import gettext_lazy as _

from common.tests.factories import (
    BaseTestMixin,
    WorkspaceFactory,
    LayerFactory,
)


class TestLayersAdvancedFilters(BaseTestMixin):
    def setUp(self):
        super().setUp()
        
        # Create a workspace for testing
        self.workspace = WorkspaceFactory(
            owner=self.user, organization=self.organization
        )
        
        # Create test layers with different attributes
        self.layer1 = LayerFactory(
            workspace=self.workspace,
            title="Active Layer 1",
            status="published",
            read_only=False,
        )
        self.layer2 = LayerFactory(
            workspace=self.workspace,
            title="Active Layer 2", 
            status="published",
            read_only=True,
        )
        self.layer3 = LayerFactory(
            workspace=self.workspace,
            title="Draft Layer 1",
            status="unpublished",
            read_only=False,
        )
        self.layer4 = LayerFactory(
            workspace=self.workspace,
            title="Draft Layer 2",
            status="unpublished", 
            read_only=True,
        )
        
        # Base GraphQL query for layers
        self.query = """
        query TestLayersQuery(
            $orgId: Int!,
            $workspaceId: Int!,
            $filterGroups: [FilterGroupInput!]
        ) {
          layers(
            orgId: $orgId,
            workspaceId: $workspaceId,
            filterGroups: $filterGroups
          ) {
            count
            data {
              id
              title
              status
              readOnly
            }
          }
        }
        """
        
        self.base_variables = {
            "orgId": self.organization.id,
            "workspaceId": self.workspace.id,
        }

    def test_query_authorization(self):
        """Test that unauthenticated users cannot access layers."""
        variables = {**self.base_variables, "filterGroups": None}
        response = self.client.execute(
            self.query, variables=variables, context=self.non_auth_request
        )
        
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))

    def test_query_without_filters(self):
        """Test basic query without any filters."""
        variables = {**self.base_variables, "filterGroups": None}
        response = self.client.execute(
            self.query, variables=variables, context=self.auth_request
        )
        
        self.assertNotIn("errors", response)
        data = response["data"]["layers"]
        
        # Should return all 4 layers
        self.assertEqual(data["count"], 4)

    def test_single_and_group_filter(self):
        """Test filtering with a single AND group."""
        variables = {
            **self.base_variables,
            "filterGroups": [
                {
                    "type": "AND",
                    "isNot": False,
                    "filters": [
                        {
                            "op": "exact",
                            "field": "status",
                            "value": "published",
                            "isNot": False
                        },
                        {
                            "op": "exact",
                            "field": "read_only",
                            "value": "false",
                            "isNot": False
                        }
                    ]
                }
            ]
        }
        
        response = self.client.execute(
            self.query, variables=variables, context=self.auth_request
        )
        
        self.assertNotIn("errors", response)
        data = response["data"]["layers"]
        
        # Should return only layer1 (published AND not read_only)
        self.assertEqual(data["count"], 1)
        self.assertEqual(data["data"][0]["id"], self.layer1.id)

    def test_single_or_group_filter(self):
        """Test filtering with a single OR group."""
        variables = {
            **self.base_variables,
            "filterGroups": [
                {
                    "type": "OR",
                    "isNot": False,
                    "filters": [
                        {
                            "op": "exact",
                            "field": "status",
                            "value": "unpublished",
                            "isNot": False
                        },
                        {
                            "op": "exact",
                            "field": "read_only",
                            "value": "true",
                            "isNot": False
                        }
                    ]
                }
            ]
        }
        
        response = self.client.execute(
            self.query, variables=variables, context=self.auth_request
        )
        
        self.assertNotIn("errors", response)
        data = response["data"]["layers"]
        
        # Should return 3 layers (layer2: read_only=true, layer3&4: unpublished)
        self.assertEqual(data["count"], 3)
        returned_ids = {layer["id"] for layer in data["data"]}
        expected_ids = {self.layer2.id, self.layer3.id, self.layer4.id}
        self.assertEqual(returned_ids, expected_ids)

    def test_multiple_groups_and_logic(self):
        """Test multiple filter groups combined with AND logic."""
        variables = {
            **self.base_variables,
            "filterGroups": [
                {
                    "type": "OR",
                    "isNot": False,
                    "filters": [
                        {
                            "op": "exact",
                            "field": "status",
                            "value": "published",
                            "isNot": False
                        },
                        {
                            "op": "exact",
                            "field": "status",
                            "value": "unpublished",
                            "isNot": False
                        }
                    ]
                },
                {
                    "type": "AND",
                    "isNot": False,
                    "filters": [
                        {
                            "op": "exact",
                            "field": "read_only",
                            "value": "false",
                            "isNot": False
                        }
                    ]
                }
            ]
        }
        
        response = self.client.execute(
            self.query, variables=variables, context=self.auth_request
        )
        
        self.assertNotIn("errors", response)
        data = response["data"]["layers"]
        
        # Should return 2 layers (layer1 and layer3: both have read_only=false)
        self.assertEqual(data["count"], 2)
        returned_ids = {layer["id"] for layer in data["data"]}
        expected_ids = {self.layer1.id, self.layer3.id}
        self.assertEqual(returned_ids, expected_ids)

    def test_group_negation(self):
        """Test group-level negation."""
        variables = {
            **self.base_variables,
            "filterGroups": [
                {
                    "type": "OR",
                    "isNot": True,  # Negate the entire group
                    "filters": [
                        {
                            "op": "exact",
                            "field": "status",
                            "value": "unpublished",
                            "isNot": False
                        }
                    ]
                }
            ]
        }
        
        response = self.client.execute(
            self.query, variables=variables, context=self.auth_request
        )
        
        self.assertNotIn("errors", response)
        data = response["data"]["layers"]
        
        # Should return 2 layers (NOT unpublished = published layers)
        self.assertEqual(data["count"], 2)
        returned_ids = {layer["id"] for layer in data["data"]}
        expected_ids = {self.layer1.id, self.layer2.id}
        self.assertEqual(returned_ids, expected_ids)

    def test_field_negation(self):
        """Test field-level negation."""
        variables = {
            **self.base_variables,
            "filterGroups": [
                {
                    "type": "AND",
                    "isNot": False,
                    "filters": [
                        {
                            "op": "exact",
                            "field": "read_only",
                            "value": "true",
                            "isNot": True  # NOT read_only = true
                        }
                    ]
                }
            ]
        }
        
        response = self.client.execute(
            self.query, variables=variables, context=self.auth_request
        )
        
        self.assertNotIn("errors", response)
        data = response["data"]["layers"]
        
        # Should return 2 layers (NOT read_only=true = read_only=false)
        self.assertEqual(data["count"], 2)
        returned_ids = {layer["id"] for layer in data["data"]}
        expected_ids = {self.layer1.id, self.layer3.id}
        self.assertEqual(returned_ids, expected_ids)

    def test_contains_filter(self):
        """Test text contains filtering."""
        variables = {
            **self.base_variables,
            "filterGroups": [
                {
                    "type": "AND",
                    "isNot": False,
                    "filters": [
                        {
                            "op": "icontains",
                            "field": "title",
                            "value": "active",
                            "isNot": False
                        }
                    ]
                }
            ]
        }
        
        response = self.client.execute(
            self.query, variables=variables, context=self.auth_request
        )
        
        self.assertNotIn("errors", response)
        data = response["data"]["layers"]
        
        # Should return 2 layers (titles containing "active")
        self.assertEqual(data["count"], 2)
        returned_ids = {layer["id"] for layer in data["data"]}
        expected_ids = {self.layer1.id, self.layer2.id}
        self.assertEqual(returned_ids, expected_ids)

    def test_complex_nested_logic(self):
        """Test complex nested logic: (status=published OR status=unpublished) AND NOT(read_only=true)"""
        variables = {
            **self.base_variables,
            "filterGroups": [
                {
                    "type": "OR",
                    "isNot": False,
                    "filters": [
                        {
                            "op": "exact",
                            "field": "status",
                            "value": "published",
                            "isNot": False
                        },
                        {
                            "op": "exact",
                            "field": "status",
                            "value": "unpublished",
                            "isNot": False
                        }
                    ]
                },
                {
                    "type": "AND",
                    "isNot": True,  # NOT group
                    "filters": [
                        {
                            "op": "exact",
                            "field": "read_only",
                            "value": "true",
                            "isNot": False
                        }
                    ]
                }
            ]
        }
        
        response = self.client.execute(
            self.query, variables=variables, context=self.auth_request
        )
        
        self.assertNotIn("errors", response)
        data = response["data"]["layers"]
        
        # Should return 2 layers (all layers have status published/unpublished, 
        # but exclude read_only=true, so only layer1 and layer3)
        self.assertEqual(data["count"], 2)
        returned_ids = {layer["id"] for layer in data["data"]}
        expected_ids = {self.layer1.id, self.layer3.id}
        self.assertEqual(returned_ids, expected_ids)

    def test_empty_filter_groups(self):
        """Test behavior with empty filter groups."""
        variables = {
            **self.base_variables,
            "filterGroups": []
        }
        
        response = self.client.execute(
            self.query, variables=variables, context=self.auth_request
        )
        
        self.assertNotIn("errors", response)
        data = response["data"]["layers"]
        
        # Should return all layers when no filters are applied
        self.assertEqual(data["count"], 4)

    def test_invalid_workspace_id(self):
        """Test query with invalid workspace ID."""
        variables = {
            "orgId": self.organization.id,
            "workspaceId": 99999,  # Non-existent workspace
            "filterGroups": None
        }

        response = self.client.execute(
            self.query, variables=variables, context=self.auth_request
        )

        self.assertIn("errors", response)
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"workspace_id": _("Invalid workspace_id") % {}}
        )

    def test_backward_compatibility_with_legacy_filters(self):
        """Test that legacy filters parameter still works."""
        legacy_query = """
        query TestLayersLegacyQuery(
            $orgId: Int!,
            $workspaceId: Int!,
            $filters: [DjangoFilterInput!]
        ) {
          layers(
            orgId: $orgId,
            workspaceId: $workspaceId,
            filters: $filters
          ) {
            count
            data {
              id
              title
              status
            }
          }
        }
        """

        variables = {
            **self.base_variables,
            "filters": [
                {
                    "field": "status",
                    "value": "published",
                    "clause": "exact"
                }
            ]
        }

        response = self.client.execute(
            legacy_query, variables=variables, context=self.auth_request
        )

        self.assertNotIn("errors", response)
        data = response["data"]["layers"]

        # Should return 2 published layers
        self.assertEqual(data["count"], 2)
        returned_ids = {layer["id"] for layer in data["data"]}
        expected_ids = {self.layer1.id, self.layer2.id}
        self.assertEqual(returned_ids, expected_ids)

    def test_pk_parameter_with_filter_groups(self):
        """Test pk parameter combined with filter groups."""
        query_with_pk = """
        query TestLayersWithPk(
            $orgId: Int!,
            $workspaceId: Int!,
            $pk: Int!,
            $filterGroups: [FilterGroupInput!]
        ) {
          layers(
            orgId: $orgId,
            workspaceId: $workspaceId,
            pk: $pk,
            filterGroups: $filterGroups
          ) {
            count
            data {
              id
              title
            }
          }
        }
        """

        variables = {
            **self.base_variables,
            "pk": self.layer1.id,
            "filterGroups": [
                {
                    "type": "AND",
                    "isNot": False,
                    "filters": [
                        {
                            "op": "exact",
                            "field": "status",
                            "value": "published",
                            "isNot": False
                        }
                    ]
                }
            ]
        }

        response = self.client.execute(
            query_with_pk, variables=variables, context=self.auth_request
        )

        self.assertNotIn("errors", response)
        data = response["data"]["layers"]

        # Should return only layer1 (pk filter AND status filter)
        self.assertEqual(data["count"], 1)
        self.assertEqual(data["data"][0]["id"], self.layer1.id)
