# Advanced Filter System Documentation

## Overview

The new Advanced Filter System provides a simplified, two-level filtering structure that replaces the complex recursive nested filtering. This system is more predictable, easier to understand, and provides better performance.

## Structure

### AdvancedFilterInput
```graphql
input AdvancedFilterInput {
  advancedFilters: [FilterGroupInput!]!
}
```

### FilterGroupInput
```graphql
input FilterGroupInput {
  type: GroupTypeEnum!  # "AND" or "OR"
  isNot: Boolean        # negates the entire group
  filters: [FieldFilterInput!]!
}
```

### FieldFilterInput
```graphql
input FieldFilterInput {
  op: FilterOperatorEnum!  # field operators only (exact, icontains, gt, etc.)
  field: String!
  value: String!
  isNot: Boolean          # negates this specific filter
}
```

### Enums
```graphql
enum GroupTypeEnum {
  AND
  OR
}

enum FilterOperatorEnum {
  exact
  iexact
  contains
  icontains
  values_in
  gt
  gte
  lt
  lte
  range
  date__gt
  date__lt
  date__gte
  date__lte
  time__gt
  time__lt
  time__gte
  time__lte
  isnull
  isempty
}
```

## Logic

1. **Top Level**: All `FilterGroupInput` objects are combined with **AND** logic
2. **Group Level**: Within each group, `FieldFilterInput` objects are combined using the group's `type` (AND/OR)
3. **Group Negation**: `isNot` at group level negates the entire group result
4. **Field Negation**: `isNot` at field level negates the individual field filter

## Examples

### Basic Single Group Filter

```graphql
query {
  layers(
    orgId: 1,
    workspaceId: 2,
    advancedFilter: {
      advancedFilters: [
        {
          type: AND,
          isNot: false,
          filters: [
            {
              op: exact,
              field: "status",
              value: "published",
              isNot: false
            },
            {
              op: gt,
              field: "records_count",
              value: "100",
              isNot: false
            }
          ]
        }
      ]
    }
  ) {
    data { id title status }
    count
  }
}
```

**Logic**: `status = 'published' AND records_count > 100`

### OR Group Filter

```graphql
query {
  records(
    orgId: 1,
    layerId: 123,
    advancedFilter: {
      advancedFilters: [
        {
          type: OR,
          isNot: false,
          filters: [
            {
              op: exact,
              field: "status",
              value: "active",
              isNot: false
            },
            {
              op: exact,
              field: "status",
              value: "pending",
              isNot: false
            }
          ]
        }
      ]
    }
  ) {
    data { id status }
    count
  }
}
```

**Logic**: `status = 'active' OR status = 'pending'`

### Multiple Groups (AND Combined)

```graphql
query {
  records(
    orgId: 1,
    layerId: 123,
    advancedFilter: {
      advancedFilters: [
        {
          type: OR,
          isNot: false,
          filters: [
            {
              op: exact,
              field: "category",
              value: "type1",
              isNot: false
            },
            {
              op: exact,
              field: "category",
              value: "type2",
              isNot: false
            }
          ]
        },
        {
          type: AND,
          isNot: false,
          filters: [
            {
              op: gte,
              field: "weight",
              value: "10",
              isNot: false
            },
            {
              op: lte,
              field: "weight",
              value: "50",
              isNot: false
            }
          ]
        }
      ]
    }
  ) {
    data { id category weight }
    count
  }
}
```

**Logic**: `(category = 'type1' OR category = 'type2') AND (weight >= 10 AND weight <= 50)`

### Group Negation Example

```graphql
query {
  layers(
    orgId: 1,
    workspaceId: 2,
    advancedFilter: {
      advancedFilters: [
        {
          type: OR,
          isNot: true,  # Negates the entire group
          filters: [
            {
              op: exact,
              field: "status",
              value: "deleted",
              isNot: false
            },
            {
              op: exact,
              field: "status",
              value: "archived",
              isNot: false
            }
          ]
        }
      ]
    }
  ) {
    data { id status }
    count
  }
}
```

**Logic**: `NOT (status = 'deleted' OR status = 'archived')`

### Field Negation Example

```graphql
query {
  records(
    orgId: 1,
    layerId: 123,
    advancedFilter: {
      advancedFilters: [
        {
          type: AND,
          isNot: false,
          filters: [
            {
              op: exact,
              field: "status",
              value: "deleted",
              isNot: true  # Negates this specific filter
            },
            {
              op: gt,
              field: "weight",
              value: "0",
              isNot: false
            }
          ]
        }
      ]
    }
  ) {
    data { id status weight }
    count
  }
}
```

**Logic**: `NOT (status = 'deleted') AND weight > 0`

### Complex Example

The example from your requirements:
`(NOT (filter1 OR filter2 OR filter3 OR filter4)) AND (NOT (filter5 AND filter6 AND filter7 AND filter8)) AND (NOT (filter9 OR filter10 OR filter11 OR filter12))`

```graphql
query {
  records(
    orgId: 1,
    layerId: 123,
    advancedFilter: {
      advancedFilters: [
        {
          type: OR,
          isNot: true,
          filters: [
            { op: exact, field: "field1", value: "value1", isNot: false },
            { op: exact, field: "field2", value: "value2", isNot: false },
            { op: exact, field: "field3", value: "value3", isNot: false },
            { op: exact, field: "field4", value: "value4", isNot: false }
          ]
        },
        {
          type: AND,
          isNot: true,
          filters: [
            { op: exact, field: "field5", value: "value5", isNot: false },
            { op: exact, field: "field6", value: "value6", isNot: false },
            { op: exact, field: "field7", value: "value7", isNot: false },
            { op: exact, field: "field8", value: "value8", isNot: false }
          ]
        },
        {
          type: OR,
          isNot: true,
          filters: [
            { op: exact, field: "field9", value: "value9", isNot: false },
            { op: exact, field: "field10", value: "value10", isNot: false },
            { op: exact, field: "field11", value: "value11", isNot: false },
            { op: exact, field: "field12", value: "value12", isNot: false }
          ]
        }
      ]
    }
  ) {
    data { id }
    count
  }
}
```

## Backward Compatibility

The system maintains full backward compatibility:

1. **Legacy `filters`**: Still works exactly as before
2. **Deprecated `nested_filter`**: Now treated as `advanced_filter` for compatibility
3. **New `advanced_filter`**: The recommended approach

## Migration Guide

### From Legacy Filters
```graphql
# Old way
filters: [
  { field: "status", value: "active", clause: exact }
]

# New way
advancedFilter: {
  advancedFilters: [
    {
      type: AND,
      isNot: false,
      filters: [
        { op: exact, field: "status", value: "active", isNot: false }
      ]
    }
  ]
}
```

### From Nested Filters
The old recursive nested filters are replaced with this simpler two-level structure. Complex nested logic should be flattened into multiple groups combined with AND logic.

## Benefits

1. **Predictable Structure**: Exactly two levels, no deep nesting
2. **Better Performance**: No recursion, simpler processing
3. **Easier to Understand**: Clear separation between group logic and field logic
4. **Type Safety**: Strong typing with GraphQL enums
5. **Flexible**: Supports complex queries while remaining manageable
