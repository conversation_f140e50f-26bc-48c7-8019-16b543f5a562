# Advanced Filter System Test Suite

## Overview

This document describes the comprehensive test suite for the Advanced Filter System implemented for the `layers` and `records` GraphQL queries. The test suite covers all aspects of the two-level filtering structure with groups and individual filters.

## Test Structure

### Test Files

1. **`test_layers_advanced_filters.py`** - Tests for layers query filtering
2. **`test_records_advanced_filters.py`** - Tests for records query filtering  
3. **`test_advanced_filters_edge_cases.py`** - Edge cases and error handling tests

### Test Data Setup

Each test file creates appropriate test data:

- **Layers Tests**: Creates 4 layers with different `status` (published/unpublished) and `read_only` (true/false) combinations
- **Records Tests**: Creates 4 records with varied `weight`, `source_properties`, `map_data`, and `data` fields
- **Edge Cases**: Creates minimal test data to focus on error conditions

## Test Categories

### 1. Basic Functionality Tests

#### Single Group Filters
- ✅ **AND Group**: Tests filters combined with AND logic within a group
- ✅ **OR Group**: Tests filters combined with OR logic within a group
- ✅ **Empty Groups**: Tests behavior with empty filter groups

#### Multiple Groups
- ✅ **Multiple AND Groups**: Tests multiple groups combined with AND logic at top level
- ✅ **Mixed Group Types**: Tests combination of AND and OR groups

### 2. Negation Tests

#### Field-Level Negation
- ✅ **Field `isNot`**: Tests negation of individual field filters
- ✅ **Multiple Field Negations**: Tests multiple negated fields in same group

#### Group-Level Negation  
- ✅ **Group `isNot`**: Tests negation of entire filter groups
- ✅ **Nested Negations**: Tests combination of group and field negations

### 3. Operator Tests

#### Text Operators
- ✅ **exact**: Exact string matching
- ✅ **iexact**: Case-insensitive exact matching
- ✅ **contains**: Substring matching
- ✅ **icontains**: Case-insensitive substring matching

#### Numeric Operators
- ✅ **gt**: Greater than comparison
- ✅ **gte**: Greater than or equal comparison
- ✅ **lt**: Less than comparison
- ✅ **lte**: Less than or equal comparison

#### Special Operators
- ✅ **isnull**: Null value checking
- ✅ **date__gt/lt/gte/lte**: Date comparisons

### 4. Field Type Tests

#### JSON Field Filtering
- ✅ **Simple JSON Fields**: Filtering on `data__status`, `map_data__category`
- ✅ **Nested JSON Fields**: Filtering on `source_properties__city`, `source_properties__poi_type`
- ✅ **Deep Nesting**: Tests deeply nested JSON field access (`data__level1__level2__level3__deep_value`)

#### Standard Fields
- ✅ **Integer Fields**: Filtering on `weight`, `id`
- ✅ **String Fields**: Filtering on `title`, `status`
- ✅ **Boolean Fields**: Filtering on `read_only`

### 5. Complex Logic Tests

#### Real-World Scenarios
- ✅ **Multi-Criteria Search**: `(city = 'Riyadh' OR city = 'Jeddah') AND weight > 30`
- ✅ **Exclusion Filters**: `(High OR Medium confidence) AND NOT pending status`
- ✅ **Category Filtering**: `(status = published OR unpublished) AND NOT read_only`

#### Edge Case Logic
- ✅ **Double Negation**: `NOT(NOT(condition))` = `condition`
- ✅ **Empty Result Sets**: Filters that match no records
- ✅ **All Records**: Filters that match all records

### 6. Authorization & Security Tests

#### Authentication
- ✅ **Unauthenticated Access**: Verifies proper rejection of unauthenticated requests
- ✅ **Authenticated Access**: Verifies proper handling of authenticated requests

#### Data Isolation
- ✅ **Organization Isolation**: Tests that users only see their organization's data
- ✅ **Workspace Isolation**: Tests that layers are properly scoped to workspaces

### 7. Backward Compatibility Tests

#### Legacy Filter Support
- ✅ **Legacy `filters` Parameter**: Tests that old filter format still works
- ✅ **Mixed Parameters**: Tests behavior when both old and new filters are provided

#### Parameter Combinations
- ✅ **PK + Filter Groups**: Tests `pk` parameter combined with filter groups
- ✅ **Page Info**: Tests pagination with advanced filters

### 8. Error Handling & Validation Tests

#### GraphQL Validation
- ✅ **Missing Required Fields**: Tests validation of required `field`, `value`, `op` parameters
- ✅ **Invalid Enum Values**: Tests validation of `op` and `type` enum values
- ✅ **Type Mismatches**: Tests handling of incorrect parameter types

#### Data Validation
- ✅ **Invalid Numeric Values**: Tests numeric operators with non-numeric values
- ✅ **Invalid Date Formats**: Tests date operators with malformed dates
- ✅ **Non-Existent Fields**: Tests filtering on fields that don't exist

#### Performance & Limits
- ✅ **Large Filter Groups**: Tests performance with many filter groups (50+)
- ✅ **Unicode Support**: Tests filtering with Unicode and special characters
- ✅ **Null Value Handling**: Tests filtering with null/empty values

### 9. Integration Tests

#### End-to-End Scenarios
- ✅ **Complete Query Flow**: Tests full GraphQL query execution with filters
- ✅ **Database Integration**: Tests actual database query generation and execution
- ✅ **Result Formatting**: Tests proper formatting of filtered results

## Test Execution

### Running Tests

```bash
# Run all tests with pytest
python run_advanced_filter_tests.py pytest

# Run with Django test runner
python run_advanced_filter_tests.py django

# Run with coverage analysis
python run_advanced_filter_tests.py coverage

# Run specific test class
python run_advanced_filter_tests.py class:TestLayersAdvancedFilters
```

### Test Coverage Goals

- **Function Coverage**: 100% of filter-related functions
- **Branch Coverage**: All logical branches in filter processing
- **Edge Case Coverage**: All error conditions and edge cases
- **Integration Coverage**: End-to-end query execution paths

## Expected Test Results

### Success Criteria
- All tests pass without errors
- No memory leaks or performance issues
- Proper error messages for invalid inputs
- Consistent behavior across different data types

### Performance Benchmarks
- Single filter group: < 100ms
- Multiple filter groups: < 500ms
- Large filter sets (50+ groups): < 2s
- Complex nested JSON filtering: < 200ms

## Test Data Examples

### Layer Test Data
```python
layer1 = LayerFactory(title="Active Layer 1", status="published", read_only=False)
layer2 = LayerFactory(title="Active Layer 2", status="published", read_only=True)
layer3 = LayerFactory(title="Draft Layer 1", status="unpublished", read_only=False)
layer4 = LayerFactory(title="Draft Layer 2", status="unpublished", read_only=True)
```

### Record Test Data
```python
record1 = RecordFactory(
    weight=25,
    source_properties={"city": "Riyadh", "poi_type": "schools"},
    data={"status": "active"}
)
record2 = RecordFactory(
    weight=75,
    source_properties={"city": "Jeddah", "poi_type": "hospitals"},
    data={"status": "active"}
)
```

## Maintenance

### Adding New Tests
1. Follow existing test patterns and naming conventions
2. Include both positive and negative test cases
3. Test edge cases and error conditions
4. Update this documentation with new test categories

### Test Data Management
- Use factories for consistent test data creation
- Clean up test data after each test
- Use meaningful test data that reflects real-world usage

This comprehensive test suite ensures the Advanced Filter System is robust, performant, and handles all expected use cases correctly.
