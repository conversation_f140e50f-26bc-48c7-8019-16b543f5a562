# Frontend User Story: Proactive Permission-Based UI Management

## 📋 Story Overview

**As a frontend developer**, I want to implement proactive permission-based UI management so that users only see and can interact with UI elements they have permission to use, eliminating the need for reactive error handling and improving user experience.

## 🎯 Background Context

Currently, the frontend handles `Forbidden` errors reactively after users attempt actions they don't have permission for. We want to shift to a proactive approach where the UI reflects the user's actual permissions before they attempt any actions.

The backend provides a comprehensive permissions system with hierarchical access control:
- **Organization Level**: Primary permission boundary
- **Workspace Level**: Secondary resource-specific permissions  
- **Resource Level**: Individual resource ownership (layers, records, etc.)

## ✅ Acceptance Criteria

### AC1: Permission Data Fetching
- [ ] Implement a permissions query that fetches user permissions for the current organization and workspace context
- [ ] Cache permission data appropriately to avoid excessive API calls
- [ ] Refresh permissions when organization or workspace context changes
- [ ] Handle permission data loading states in the UI

### AC2: Organization-Level Permission Controls
- [ ] Hide/disable "Add User" buttons when user lacks `ADD_USER` permission
- [ ] Hide/disable "Manage Users" sections when user lacks `CHANGE_USER` permission
- [ ] Hide/disable "Create Workspace" buttons when user lacks `ADD_WORKSPACE` permission
- [ ] Hide/disable organization settings when user lacks `CHANGE_ORGANIZATION` permission

### AC3: Workspace-Level Permission Controls
- [ ] Hide/disable workspace edit buttons when user lacks `CHANGE_WORKSPACE` permission
- [ ] Hide/disable workspace delete buttons when user lacks `DELETE_WORKSPACE` permission
- [ ] Show read-only workspace views when user only has `VIEW_WORKSPACE` permission
- [ ] Hide workspace management sections entirely when user has no workspace permissions

### AC4: Layer-Level Permission Controls
- [ ] Hide/disable "Create Layer" buttons when user lacks `ADD_LAYER` permission
- [ ] Hide/disable layer edit buttons when user lacks `EDIT_LAYER` permission
- [ ] Hide/disable layer delete buttons when user lacks `DELETE_LAYER` permission
- [ ] Show read-only layer views when user only has `VIEW_LAYER` permission

### AC5: Record-Level Permission Controls
- [ ] Hide/disable "Add Record" buttons when user lacks `ADD_RECORD` permission
- [ ] Hide/disable record edit buttons when user lacks `EDIT_RECORD` permission
- [ ] Hide/disable record delete buttons when user lacks `DELETE_RECORD` permission
- [ ] Show read-only record views when user only has `VIEW_RECORD` permission

### AC6: Multi-Level Permission Handling
- [ ] Implement permission checks that consider both organization AND workspace permissions
- [ ] Handle cases where user has organization permissions but not workspace-specific permissions
- [ ] Properly handle ownership-based permissions (workspace owner, record creator, etc.)

### AC7: Superuser and Special Cases
- [ ] Show all UI elements for superusers regardless of explicit permissions
- [ ] Handle ownership scenarios (workspace owners see edit controls even without explicit permissions)
- [ ] Gracefully handle permission edge cases and loading states

### AC8: Chat AI Permission Integration
- [ ] Hide/disable Chat AI features when user lacks `CHANGE_WORKSPACE` permission
- [ ] Ensure Chat AI workspace selection respects workspace permissions
- [ ] Hide Chat AI entirely if user has no workspace access

## 🔧 Technical Implementation Guide

### 1. Permission Data Structure

```typescript
interface UserPermissions {
  organization: {
    id: number;
    permissions: string[]; // ['ADD_USER', 'CHANGE_WORKSPACE', etc.]
  };
  workspace?: {
    id: number;
    permissions: string[];
    isOwner: boolean;
  };
  isSuperuser: boolean;
}
```

### 2. Permission Constants

```typescript
// Organization-level permissions
const ORG_PERMISSIONS = {
  VIEW_USER: 'view_user',
  ADD_USER: 'add_user',
  CHANGE_USER: 'change_user',
  DELETE_USER: 'delete_user',
  VIEW_ORGANIZATION: 'view_organization',
  CHANGE_ORGANIZATION: 'change_organization',
  DELETE_ORGANIZATION: 'delete_organization',
  ADD_ROLE: 'add_role',
  CHANGE_ROLE: 'change_role',
  DELETE_ROLE: 'delete_role',
} as const;

// Workspace-level permissions
const WORKSPACE_PERMISSIONS = {
  ADD_WORKSPACE: 'add_workspace',
  CHANGE_WORKSPACE: 'change_workspace',
  DELETE_WORKSPACE: 'delete_workspace',
  VIEW_WORKSPACE: 'view_workspace',
} as const;

// Layer-level permissions
const LAYER_PERMISSIONS = {
  VIEW_LAYER: 'view_layer',
  ADD_LAYER: 'add_layer',
  DELETE_LAYER: 'delete_layer',
  EDIT_LAYER: 'edit_layer',
} as const;

// Record-level permissions
const RECORD_PERMISSIONS = {
  VIEW_RECORD: 'view_record',
  ADD_RECORD: 'add_record',
  DELETE_RECORD: 'delete_record',
  EDIT_RECORD: 'edit_record',
} as const;
```

### 3. Permission Hook Implementation

```typescript
const usePermissions = () => {
  const { data: permissions, loading, error } = usePermissionsQuery();
  
  const hasOrgPermission = (permission: string): boolean => {
    if (permissions?.isSuperuser) return true;
    return permissions?.organization?.permissions?.includes(permission) || false;
  };
  
  const hasWorkspacePermission = (permission: string): boolean => {
    if (permissions?.isSuperuser) return true;
    if (permissions?.workspace?.isOwner) return true;
    return permissions?.workspace?.permissions?.includes(permission) || false;
  };
  
  const hasMultiLevelPermission = (orgPerm: string, workspacePerm: string): boolean => {
    return hasOrgPermission(orgPerm) && hasWorkspacePermission(workspacePerm);
  };
  
  return {
    permissions,
    loading,
    error,
    hasOrgPermission,
    hasWorkspacePermission,
    hasMultiLevelPermission,
    isSuperuser: permissions?.isSuperuser || false,
  };
};
```

## 💻 UI Component Examples

### User Management Section

```typescript
const UserManagementSection = () => {
  const { hasOrgPermission } = usePermissions();
  
  const canAddUsers = hasOrgPermission(ORG_PERMISSIONS.ADD_USER);
  const canManageUsers = hasOrgPermission(ORG_PERMISSIONS.CHANGE_USER);
  
  if (!canManageUsers && !canAddUsers) {
    return null; // Hide entire section
  }
  
  return (
    <div>
      <h2>User Management</h2>
      {canAddUsers && (
        <Button onClick={handleAddUser}>Add User</Button>
      )}
      {canManageUsers && (
        <UserList editable={true} />
      )}
      {!canManageUsers && (
        <UserList editable={false} />
      )}
    </div>
  );
};
```

### Workspace Actions

```typescript
const WorkspaceActions = ({ workspace }) => {
  const { hasMultiLevelPermission } = usePermissions();
  
  const canEdit = hasMultiLevelPermission(
    ORG_PERMISSIONS.CHANGE_WORKSPACE, 
    WORKSPACE_PERMISSIONS.CHANGE_WORKSPACE
  );
  const canDelete = hasMultiLevelPermission(
    ORG_PERMISSIONS.DELETE_WORKSPACE,
    WORKSPACE_PERMISSIONS.DELETE_WORKSPACE
  );
  
  return (
    <div>
      {canEdit && (
        <Button onClick={() => editWorkspace(workspace.id)}>
          Edit Workspace
        </Button>
      )}
      {canDelete && (
        <Button variant="danger" onClick={() => deleteWorkspace(workspace.id)}>
          Delete Workspace
        </Button>
      )}
    </div>
  );
};
```

### Layer Management

```typescript
const LayerManagement = ({ layer }) => {
  const { hasMultiLevelPermission } = usePermissions();
  
  const canAddLayers = hasMultiLevelPermission(
    ORG_PERMISSIONS.ADD_LAYER,
    WORKSPACE_PERMISSIONS.CHANGE_WORKSPACE
  );
  const canEditLayer = hasMultiLevelPermission(
    ORG_PERMISSIONS.EDIT_LAYER,
    WORKSPACE_PERMISSIONS.CHANGE_WORKSPACE
  );
  const canDeleteLayer = hasMultiLevelPermission(
    ORG_PERMISSIONS.DELETE_LAYER,
    WORKSPACE_PERMISSIONS.DELETE_WORKSPACE
  );
  
  return (
    <div>
      {canAddLayers && (
        <Button onClick={handleCreateLayer}>Create Layer</Button>
      )}
      {layer && (
        <div>
          {canEditLayer && (
            <Button onClick={() => editLayer(layer.id)}>Edit Layer</Button>
          )}
          {canDeleteLayer && (
            <Button variant="danger" onClick={() => deleteLayer(layer.id)}>
              Delete Layer
            </Button>
          )}
        </div>
      )}
    </div>
  );
};
```

### Chat AI Integration

```typescript
const ChatAISection = () => {
  const { hasMultiLevelPermission } = usePermissions();
  
  const canUseChatAI = hasMultiLevelPermission(
    ORG_PERMISSIONS.CHANGE_WORKSPACE,
    WORKSPACE_PERMISSIONS.CHANGE_WORKSPACE
  );
  
  if (!canUseChatAI) {
    return null; // Hide Chat AI entirely
  }
  
  return <ChatAIComponent />;
};
```

### Permission Context Provider

```typescript
const PermissionProvider = ({ children, organizationId, workspaceId }) => {
  const [permissions, setPermissions] = useState(null);

  useEffect(() => {
    // Fetch permissions when org/workspace context changes
    fetchUserPermissions(organizationId, workspaceId)
      .then(setPermissions);
  }, [organizationId, workspaceId]);

  return (
    <PermissionContext.Provider value={permissions}>
      {children}
    </PermissionContext.Provider>
  );
};
```

## 🚀 Implementation Phases

### Phase 1: Core Infrastructure
| Priority | Task | Description |
|----------|------|-------------|
| High | Permission Query | Implement permission query and caching mechanism |
| High | Permission Hooks | Create permission hooks and utility functions |
| High | Permission Constants | Set up permission constants matching backend |
| Medium | Context Provider | Implement permission context provider |

### Phase 2: Organization-Level Controls
| Priority | Task | Description |
|----------|------|-------------|
| High | User Management | Implement user management permission controls |
| Medium | Organization Settings | Add organization settings permission controls |
| Medium | Role Management | Implement role management permission controls |

### Phase 3: Workspace-Level Controls
| Priority | Task | Description |
|----------|------|-------------|
| High | Workspace CRUD | Implement workspace CRUD permission controls |
| High | Multi-level Checks | Add multi-level permission checking logic |
| Medium | Ownership Permissions | Handle ownership-based permission scenarios |

### Phase 4: Resource-Level Controls
| Priority | Task | Description |
|----------|------|-------------|
| High | Layer Management | Implement layer management permission controls |
| High | Record Management | Add record management permission controls |
| Medium | Chat AI Integration | Integrate Chat AI permission controls |

### Phase 5: Polish and Edge Cases
| Priority | Task | Description |
|----------|------|-------------|
| Medium | Loading States | Handle loading states and error scenarios |
| Medium | Superuser Scenarios | Implement superuser bypass functionality |
| Low | Permission Refresh | Add real-time permission refresh mechanisms |

## 🧪 Testing Considerations

### Test Scenarios
- **Different User Roles**: Test with organization owner, workspace owner, regular user
- **Permission Changes**: Test real-time permission updates when user roles change
- **Multi-level Permissions**: Test scenarios requiring both organization and workspace permissions
- **Superuser Functionality**: Test superuser bypass functionality
- **Loading States**: Test graceful degradation when permissions are loading
- **Edge Cases**: Test workspace ownership vs. explicit permissions

### Test Cases by Permission Level

#### Organization Level
```typescript
describe('Organization Permissions', () => {
  it('should hide Add User button when user lacks ADD_USER permission', () => {
    // Test implementation
  });

  it('should show read-only user list when user lacks CHANGE_USER permission', () => {
    // Test implementation
  });
});
```

#### Workspace Level
```typescript
describe('Workspace Permissions', () => {
  it('should hide workspace edit controls when user lacks CHANGE_WORKSPACE', () => {
    // Test implementation
  });

  it('should show workspace controls for workspace owners', () => {
    // Test implementation
  });
});
```

#### Multi-Level Permissions
```typescript
describe('Multi-Level Permissions', () => {
  it('should require both org and workspace permissions for layer operations', () => {
    // Test implementation
  });
});
```

## 📊 Success Metrics

### Primary Metrics
- **Reduction in Forbidden Errors**: Target 90% reduction in `Forbidden` error occurrences
- **User Experience Score**: Improved user satisfaction scores related to interface clarity
- **Task Completion Rate**: Increased successful task completion without permission errors

### Secondary Metrics
- **UI Clarity**: Users report cleaner, more intuitive interface
- **Workflow Efficiency**: Reduced time spent on unavailable actions
- **Support Tickets**: Decreased permission-related support requests

### Technical Metrics
- **Permission Query Performance**: Sub-200ms permission data fetching
- **Cache Hit Rate**: >80% cache hit rate for permission data
- **Error Rate**: <1% permission-related UI errors

## 🔍 Monitoring and Maintenance

### Performance Monitoring
- Track permission query response times
- Monitor cache effectiveness
- Measure UI rendering performance with permission checks

### Error Tracking
- Log permission check failures
- Track edge cases and unexpected scenarios
- Monitor fallback behavior effectiveness

### User Feedback
- Collect feedback on UI clarity and intuitiveness
- Track user confusion related to missing features
- Monitor accessibility compliance with hidden/disabled elements

## 📚 Additional Resources

### Backend Permission System Reference
- Organization permissions: `organizations/perms_constants.py`
- Workspace ACL: `workspaces/models/workspace.py`
- Layer permissions: `layers/permissions/layer.py`
- Record permissions: `layers/permissions/record.py`

### Related Documentation
- Backend API documentation for permissions query
- User role management documentation
- Organization and workspace hierarchy documentation

---

**Document Version**: 1.0
**Last Updated**: [Current Date]
**Maintained By**: Frontend Development Team
